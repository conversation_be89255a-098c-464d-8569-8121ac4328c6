import os
import asyncio
import subprocess
from playwright.async_api import async_playwright
import logging

logger = logging.getLogger(__name__)

async def convert_html_to_pdf(html_content: str, pdf_output_path: str) -> bool:
    try:
        async with async_playwright() as p:
            browser = await p.chromium.launch()
            page = await browser.new_page()

            await page.set_content(html_content, timeout=80_000)

            pdf_bytes = await page.pdf(
                width="14.8in",
                height="21in",
                margin={"top": "0", "right": "0", "bottom": "0", "left": "0"},
                print_background=True,
                scale=1
            )

            await browser.close()

            # Try to use Ghostscript for compression, but fallback to direct PDF if it fails
            try:
                ghostscript_cmd = [
                    'gswin64c',
                    '-sDEVICE=pdfwrite',
                    '-dPDFSETTINGS=/ebook',
                    '-dNOPAUSE',
                    '-dQUIET',
                    '-dBATCH',
                    '-sOutputFile=-',
                    '-'
                ]

                proc = await asyncio.to_thread(
                    subprocess.run,
                    ghostscript_cmd,
                    input=pdf_bytes,
                    capture_output=True,
                    check=True
                )

                with open(pdf_output_path, 'wb') as f:
                    f.write(proc.stdout)

                logger.info(f"Successfully created compressed PDF: {pdf_output_path}")
                return True

            except (subprocess.CalledProcessError, FileNotFoundError) as gs_error:
                # Ghostscript failed or not found, use direct PDF
                logger.warning(f"Ghostscript compression failed ({gs_error}), using direct PDF")

                with open(pdf_output_path, 'wb') as f:
                    f.write(pdf_bytes)

                logger.info(f"Successfully created PDF (uncompressed): {pdf_output_path}")
                return True

    except Exception as e:
        if os.path.exists(pdf_output_path):
            os.remove(pdf_output_path)
        logger.error(f"Error during PDF conversion: {str(e)}, file: {pdf_output_path}")
        return False


async def convert_html_to_one_page_pdf(html_content: str, pdf_output_path: str) -> bool:
    """
    Convert HTML content to a single-page PDF that fits all content on one page.
    This prevents charts and content from being broken across multiple pages.
    """
    try:
        async with async_playwright() as p:
            browser = await p.chromium.launch()
            page = await browser.new_page()

            await page.set_content(html_content, timeout=80_000)

            # Get the content height to determine appropriate page size
            content_height = await page.evaluate("""
                () => {
                    return Math.max(
                        document.body.scrollHeight,
                        document.body.offsetHeight,
                        document.documentElement.clientHeight,
                        document.documentElement.scrollHeight,
                        document.documentElement.offsetHeight
                    );
                }
            """)

            # Set minimum height and add some padding
            page_height = max(content_height + 100, 800)  # At least 800px, plus 100px padding

            pdf_bytes = await page.pdf(
                width="14.8in",
                height=f"{page_height}px",
                margin={"top": "20px", "right": "20px", "bottom": "20px", "left": "20px"},
                print_background=True,
                scale=0.8,  # Slightly smaller scale to ensure content fits
                prefer_css_page_size=False
            )

            await browser.close()

            # Try to use Ghostscript for compression, but fallback to direct PDF if it fails
            try:
                ghostscript_cmd = [
                    'gswin64c',
                    '-sDEVICE=pdfwrite',
                    '-dPDFSETTINGS=/ebook',
                    '-dNOPAUSE',
                    '-dQUIET',
                    '-dBATCH',
                    '-sOutputFile=-',
                    '-'
                ]

                proc = await asyncio.to_thread(
                    subprocess.run,
                    ghostscript_cmd,
                    input=pdf_bytes,
                    capture_output=True,
                    check=True
                )

                with open(pdf_output_path, 'wb') as f:
                    f.write(proc.stdout)

                logger.info(f"Successfully created compressed single-page PDF: {pdf_output_path}")
                return True

            except (subprocess.CalledProcessError, FileNotFoundError) as gs_error:
                # Ghostscript failed or not found, use direct PDF
                logger.warning(f"Ghostscript compression failed ({gs_error}), using direct single-page PDF")

                with open(pdf_output_path, 'wb') as f:
                    f.write(pdf_bytes)

                logger.info(f"Successfully created single-page PDF (uncompressed): {pdf_output_path}")
                return True

    except Exception as e:
        if os.path.exists(pdf_output_path):
            os.remove(pdf_output_path)
        logger.error(f"Error during single-page PDF conversion: {str(e)}, file: {pdf_output_path}")
        return False
